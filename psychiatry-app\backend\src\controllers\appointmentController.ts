import { Response, NextFunction } from 'express';
import { z } from 'zod';
import { AppointmentService } from '@/services/appointmentService';
import { AuthRequest } from '@/types';
import {
  createAppointmentSchema,
  updateAppointmentSchema,
} from '../utils/validation';

/**
 * Appointment controller handling HTTP requests for appointment operations
 */

// Validation schemas
const appointmentQuerySchema = z.object({
  page: z.string().optional(),
  limit: z.string().optional(),
  patientId: z.string().uuid().optional(),
  providerId: z.string().uuid().optional(),
  status: z.string().optional(),
  type: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

const cancelAppointmentSchema = z.object({
  reason: z.string().min(1, 'Cancellation reason is required').max(500),
});

const availabilityQuerySchema = z.object({
  date: z.string().refine((date) => !isNaN(Date.parse(date)), 'Invalid date format'),
});

export class AppointmentController {
  /**
   * Create a new appointment
   * POST /api/appointments
   */
  static async createAppointment(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const validatedData = createAppointmentSchema.parse(req.body);

      const result = await AppointmentService.createAppointment(
        validatedData as any,
        req.user!.id,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') || 'unknown' }
      );

      res.status(201).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get appointments with pagination and filtering
   * GET /api/appointments
   */
  static async getAppointments(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const validatedQuery = appointmentQuerySchema.parse(req.query);
      
      const result = await AppointmentService.getAppointments(
        validatedQuery,
        req.user.id,
        req.user.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get appointment by ID
   * GET /api/appointments/:id
   */
  static async getAppointmentById(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const { id } = req.params;
      
      const result = await AppointmentService.getAppointmentById(
        id,
        req.user.id,
        req.user.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update appointment
   * PUT /api/appointments/:id
   */
  static async updateAppointment(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const validatedData = updateAppointmentSchema.parse(req.body);

      const result = await AppointmentService.updateAppointment(
        req.params.id,
        validatedData,
        req.user.id,
        req.user.role,
        { ipAddress: req.ip, userAgent: req.get('User-Agent') }
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel appointment
   * POST /api/appointments/:id/cancel
   */
  static async cancelAppointment(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const { id } = req.params;
      const { reason } = cancelAppointmentSchema.parse(req.body);
      const auditData = {
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      };

      const result = await AppointmentService.cancelAppointment(
        id,
        reason,
        req.user.id,
        req.user.role,
        auditData
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get provider availability
   * GET /api/appointments/providers/:providerId/availability
   */
  static async getProviderAvailability(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      const { providerId } = req.params;
      const { date } = availabilityQuerySchema.parse(req.query);
      
      const result = await AppointmentService.getProviderAvailability(
        providerId,
        date,
        req.user.id,
        req.user.role
      );

      res.status(200).json(result);
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get appointment statistics
   * GET /api/appointments/stats
   */
  static async getAppointmentStats(req: AuthRequest, res: Response, next: NextFunction) {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
        });
      }

      // This would be implemented in AppointmentService.getStats()
      // For now, return a placeholder response
      res.status(200).json({
        success: true,
        data: {
          totalAppointments: 0,
          todayAppointments: 0,
          upcomingAppointments: 0,
          completedThisWeek: 0,
          statusDistribution: {
            SCHEDULED: 0,
            CONFIRMED: 0,
            COMPLETED: 0,
            CANCELLED: 0,
            NO_SHOW: 0,
          },
          typeDistribution: {
            CONSULTATION: 0,
            FOLLOW_UP: 0,
            THERAPY: 0,
            ASSESSMENT: 0,
            OTHER: 0,
          },
        },
        message: 'Appointment statistics retrieved successfully',
      });
    } catch (error) {
      next(error);
    }
  }
}
